<!-- Table -->
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliente
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Operatore Assegnato
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data e Ora
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nome Appuntamento
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Azione
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Azioni
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <?php $__empty_1 = true; $__currentLoopData = $appointments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $appointment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            <?php echo e($appointment->client->full_name); ?>

                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($appointment->client->email); ?>

                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <?php echo e($appointment->user->first_name); ?> <?php echo e($appointment->user->last_name); ?>

                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <?php echo e($appointment->formatted_date); ?>

                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900"><?php echo e($appointment->appointment_name); ?></div>
                        <?php if($appointment->notes): ?>
                            <div class="text-sm text-gray-500 mt-1"><?php echo e(Str::limit($appointment->notes, 50)); ?></div>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span id="status-badge-<?php echo e($appointment->id); ?>" 
                              class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($appointment->status_color); ?>">
                            <?php echo e($appointment->status_label); ?>

                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <?php if($appointment->status === 'pending'): ?>
                            <form method="POST" action="<?php echo e(route('appointments.mark-completed', $appointment)); ?>" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                        class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded-full font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
                                        onclick="return confirm('Sei sicuro di voler segnare questo appuntamento come completato?')"
                                        title="Segna come completato">
                                    <i class="fas fa-check mr-1"></i>
                                    Completa
                                </button>
                            </form>
                        <?php else: ?>
                            <span class="text-sm text-gray-500">-</span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <a href="<?php echo e(route('appointments.edit', $appointment)); ?>" 
                               class="text-blue-600 hover:text-blue-900" title="Modifica">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="<?php echo e(route('appointments.destroy', $appointment)); ?>" 
                                  method="POST" 
                                  class="inline"
                                  onsubmit="return confirm('Sei sicuro di voler eliminare questo appuntamento?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" 
                                        class="text-red-600 hover:text-red-900" title="Elimina">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="text-gray-500">
                            <i class="fas fa-calendar-times text-4xl mb-4"></i>
                            <p class="text-lg font-medium">Nessun appuntamento trovato</p>
                            <p class="text-sm">Non ci sono appuntamenti per i filtri selezionati.</p>
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/appointments/partials/table.blade.php ENDPATH**/ ?>